class CodeGenerator {
    constructor() {
        this.apiUrl = 'http://192.168.23.63:1234/v1/chat/completions';
        this.messagesContainer = document.getElementById('messages');
        this.userInput = document.getElementById('userInput');
        this.sendButton = document.getElementById('sendButton');
        this.status = document.getElementById('status');
        
        this.initializeEventListeners();
        this.autoResizeTextarea();
    }

    initializeEventListeners() {
        this.sendButton.addEventListener('click', () => this.sendMessage());
        
        this.userInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        this.userInput.addEventListener('input', () => {
            this.autoResizeTextarea();
        });
    }

    autoResizeTextarea() {
        this.userInput.style.height = 'auto';
        this.userInput.style.height = Math.min(this.userInput.scrollHeight, 200) + 'px';
    }

    async sendMessage() {
        const message = this.userInput.value.trim();
        if (!message) return;

        // Disable input while processing
        this.setInputState(false);
        
        // Add user message to chat
        this.addMessage(message, 'user');
        this.userInput.value = '';
        this.autoResizeTextarea();

        // Show typing indicator
        const typingId = this.showTypingIndicator();

        try {
            console.log('Sending message to LM Studio:', message); // Debug log
            const response = await this.callLMStudio(message);
            console.log('Received response:', response); // Debug log
            this.removeTypingIndicator(typingId);

            if (response && response.trim()) {
                this.addMessage(response, 'assistant');
            } else {
                this.addMessage('I received an empty response. Please try again.', 'assistant');
            }
        } catch (error) {
            this.removeTypingIndicator(typingId);
            this.addMessage(`Sorry, I encountered an error: ${error.message}`, 'assistant');
            console.error('Error details:', error);
        } finally {
            this.setInputState(true);
        }
    }

    async callLMStudio(message) {
        const requestBody = {
            // model: "claude-3.7-sonnet-reasoning-gemma3-12b",
            model: "google/gemma-3-12b",
            messages: [
                {
                    role: "system",
                    content: "Your name is Kigwa, You are a helpful AI assistant specialized in code generation and programming. Provide clear, well-commented code examples and explanations. When generating code, always specify the programming language and provide context about what the code does."
                },
                {
                    role: "user",
                    content: message
                }
            ],
            temperature: 0.2,
            max_tokens: 2000,
            stream: false
        };

        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer lm-studio'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const data = await response.json();
            console.log('LM Studio Response:', data); // Debug log

            if (data.choices && data.choices[0] && data.choices[0].message) {
                return data.choices[0].message.content;
            } else {
                console.error('Invalid response structure:', data);
                throw new Error('Invalid response format from LM Studio');
            }
        } catch (fetchError) {
            if (fetchError.name === 'TypeError' && fetchError.message.includes('fetch')) {
                throw new Error('Cannot connect to LM Studio. Please check if LM Studio is running and accessible at ' + this.apiUrl);
            }
            throw fetchError;
        }
    }

    addMessage(content, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // Process content for code blocks
        const processedContent = this.processCodeBlocks(content);
        messageContent.innerHTML = processedContent;
        
        messageDiv.appendChild(messageContent);
        this.messagesContainer.appendChild(messageDiv);
        
        // Scroll to bottom
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        
        // Highlight code blocks
        this.highlightCodeBlocks(messageContent);
    }

    processCodeBlocks(content) {
        // Convert markdown-style code blocks to HTML
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;

        return content.replace(codeBlockRegex, (match, language, code) => {
            const lang = language || 'text';
            const escapedCode = this.escapeHtml(code.trim());

            return `
                <div class="code-block">
                    <div class="code-header">
                        <span>${lang}</span>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <pre><code class="language-${lang}">${escapedCode}</code></pre>
                </div>
            `;
        }).replace(/\n/g, '<br>');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    highlightCodeBlocks(container) {
        const codeBlocks = container.querySelectorAll('pre code');
        codeBlocks.forEach(block => {
            if (window.Prism) {
                Prism.highlightElement(block);
            }
        });
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message assistant';
        typingDiv.id = 'typing-indicator';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = `
            <div class="typing-indicator">
                <span>AI is thinking</span>
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        typingDiv.appendChild(messageContent);
        this.messagesContainer.appendChild(typingDiv);
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        
        return 'typing-indicator';
    }

    removeTypingIndicator(id) {
        const indicator = document.getElementById(id);
        if (indicator) {
            indicator.remove();
        }
    }

    setInputState(enabled) {
        this.userInput.disabled = !enabled;
        this.sendButton.disabled = !enabled;
        
        if (enabled) {
            this.userInput.focus();
            this.status.textContent = '';
        } else {
            this.status.textContent = 'Generating response...';
        }
    }
}

// Global function for copy button
function copyCode(button) {
    const codeBlock = button.closest('.code-block');
    const code = codeBlock.querySelector('code').textContent;
    
    navigator.clipboard.writeText(code).then(() => {
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('copied');
        
        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('copied');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy code:', err);
    });
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new CodeGenerator();
});
