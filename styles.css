* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.header {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid #e0e0e0;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header p {
    color: #666;
    font-size: 1.1rem;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.messages {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.message {
    display: flex;
    gap: 1rem;
    animation: fadeIn 0.3s ease-in;
}

.message.user {
    justify-content: flex-end;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    margin-left: 20%;
}

.message.assistant .message-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    margin-right: 20%;
}

.message-content {
    padding: 1rem 1.5rem;
    border-radius: 1rem;
    max-width: 100%;
    word-wrap: break-word;
}

.message-content p {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.code-block {
    position: relative;
    margin: 1rem 0;
    border-radius: 8px;
    overflow: hidden;
    background: #2d3748;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background: #1a202c;
    color: #e2e8f0;
    font-size: 0.875rem;
}

.copy-button {
    background: #4a5568;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;
    transition: background-color 0.2s;
}

.copy-button:hover {
    background: #2d3748;
}

.copy-button.copied {
    background: #38a169;
}

pre {
    margin: 0 !important;
    padding: 1rem !important;
    background: #2d3748 !important;
    overflow-x: auto;
}

.input-container {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border-top: 1px solid #e0e0e0;
}

.input-wrapper {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
}

#userInput {
    flex: 1;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 1rem;
    font-size: 1rem;
    font-family: inherit;
    resize: none;
    min-height: 50px;
    max-height: 200px;
    transition: border-color 0.2s;
}

#userInput:focus {
    outline: none;
    border-color: #667eea;
}

.send-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s, box-shadow 0.2s;
}

.send-button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.status {
    text-align: center;
    padding: 0.5rem;
    font-size: 0.875rem;
    color: #666;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 2px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    background: #666;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

@media (max-width: 768px) {
    .container {
        margin: 0;
        border-radius: 0;
    }
    
    .header {
        padding: 1rem;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .message.user .message-content {
        margin-left: 10%;
    }
    
    .message.assistant .message-content {
        margin-right: 10%;
    }
}
