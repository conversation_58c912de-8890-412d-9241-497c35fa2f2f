* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #268d9f;
    --primary-light: #3ba3b6;
    --primary-dark: #1e7085;
    --primary-gradient: linear-gradient(135deg, #268d9f 0%, #3ba3b6 50%, #4fb3c7 100%);
    --secondary-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    --surface-color: #ffffff;
    --surface-secondary: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--surface-color);
    box-shadow: var(--shadow-xl);
    position: relative;
}

.header {
    text-align: center;
    padding: 1.5rem 2rem;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(20px);
}

.header h1 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
}

.header p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 1.5rem;
    background: var(--surface-secondary);
}

.messages {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 900px;
    margin: 0 auto;
    padding-bottom: 2rem;
}

.message {
    display: flex;
    gap: 0.75rem;
    animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    align-items: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message.user .message-content {
    background: var(--primary-gradient);
    color: white;
    margin-left: 15%;
    box-shadow: var(--shadow-md);
}

.message.assistant {
    justify-content: flex-start;
}

.message.assistant .message-content {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    margin-right: 15%;
    box-shadow: var(--shadow-sm);
}

.message-content {
    padding: 1.25rem 1.5rem;
    border-radius: var(--radius-xl);
    max-width: 100%;
    word-wrap: break-word;
    position: relative;
    font-size: 0.875rem;
    line-height: 1.7;
}

.message-content p {
    margin-bottom: 0.75rem;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content code {
    background: rgba(0, 0, 0, 0.05);
    padding: 0.125rem 0.375rem;
    border-radius: var(--radius-sm);
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    font-size: 0.8125rem;
}

.code-block {
    position: relative;
    margin: 1.25rem 0;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: #f8fafc;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1.25rem;
    background: #f1f5f9;
    color: var(--text-secondary);
    font-size: 0.8125rem;
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
}

.copy-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.copy-button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.copy-button.copied {
    background: #059669;
    color: white;
}

pre {
    margin: 0 !important;
    padding: 1.25rem !important;
    background: #ffffff !important;
    overflow-x: auto;
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace !important;
    font-size: 0.8125rem !important;
    line-height: 1.6 !important;
    color: var(--text-primary) !important;
}

pre::-webkit-scrollbar {
    height: 8px;
}

pre::-webkit-scrollbar-track {
    background: #f1f5f9;
}

pre::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

pre::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

.input-container {
    padding: 1.5rem;
    background: var(--surface-color);
    border-top: 1px solid var(--border-light);
    position: sticky;
    bottom: 0;
    backdrop-filter: blur(20px);
}

.input-wrapper {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    background: var(--surface-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: 0.75rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(38, 141, 159, 0.1);
}

#userInput {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-family: inherit;
    resize: none;
    min-height: 44px;
    max-height: 200px;
    color: var(--text-primary);
    line-height: 1.5;
}

#userInput:focus {
    outline: none;
}

#userInput::placeholder {
    color: var(--text-muted);
}

.send-button {
    background: var(--primary-gradient);
    color: white;
    border: none;
    width: 44px;
    height: 44px;
    border-radius: var(--radius-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.send-button:active {
    transform: translateY(0);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.status {
    text-align: center;
    padding: 0.75rem;
    font-size: 0.8125rem;
    color: var(--text-muted);
    font-weight: 500;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-secondary);
    font-style: italic;
    font-size: 0.875rem;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        max-width: 100%;
        margin: 0;
    }

    .messages {
        max-width: 100%;
        padding: 0 1rem;
    }

    .input-wrapper {
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    body {
        font-size: 13px;
    }

    .container {
        border-radius: 0;
        box-shadow: none;
    }

    .header {
        padding: 1rem 1.5rem;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .header p {
        font-size: 0.8125rem;
    }

    .chat-container {
        padding: 1rem;
    }

    .messages {
        gap: 1rem;
        padding-bottom: 1rem;
    }

    .message.user .message-content {
        margin-left: 5%;
    }

    .message.assistant .message-content {
        margin-right: 5%;
    }

    .message-content {
        padding: 1rem 1.25rem;
        font-size: 0.8125rem;
    }

    .input-container {
        padding: 1rem;
    }

    .input-wrapper {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    #userInput {
        padding: 0.75rem;
        font-size: 0.875rem;
    }

    .send-button {
        width: 40px;
        height: 40px;
    }

    .code-header {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }

    .copy-button {
        padding: 0.25rem 0.5rem;
        font-size: 0.6875rem;
    }

    pre {
        padding: 1rem !important;
        font-size: 0.75rem !important;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 0.75rem 1rem;
    }

    .header h1 {
        font-size: 1.25rem;
    }

    .chat-container {
        padding: 0.75rem;
    }

    .message.user .message-content {
        margin-left: 0;
    }

    .message.assistant .message-content {
        margin-right: 0;
    }

    .message-content {
        padding: 0.875rem 1rem;
    }

    .input-container {
        padding: 0.75rem;
    }
}

/* Light theme enforced */
.message-content code {
    background: rgba(38, 141, 159, 0.1);
    color: var(--primary-dark);
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar for chat container */
.chat-container::-webkit-scrollbar {
    width: 6px;
}

.chat-container::-webkit-scrollbar-track {
    background: transparent;
}

.chat-container::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Loading animation for send button */
.send-button.loading {
    animation: pulse 1.5s infinite;
}

/* Focus styles for accessibility */
.copy-button:focus,
.send-button:focus,
#userInput:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}
