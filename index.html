<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>THE DDCGROUP Code Gen- AI Assistant</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>⚡ The DDC Group CodeGen AI</h1>
            <p>Professional AI-powered code generation and programming assistance</p>
        </header>

        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message assistant">
                    <div class="message-content">
                        <p>Hello my name is Kigwa! I'm your AI code generation assistant. I can help you write code, explain programming concepts, debug issues, and more. What would you like me to help you with today?</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="input-container">
            <div class="input-wrapper">
                <textarea 
                    id="userInput" 
                    placeholder="Ask me to generate code, explain concepts, or help with programming..."
                    rows="1"
                ></textarea>
                <button id="sendButton" class="send-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m22 2-7 20-4-9-9-4 20-7z"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="status" id="status"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>
